import {HateoasResource, StringToObjectMap, StringToStringMap} from "@eccosolutions/ecco-common";
import {Address, AddressHistoryDto} from "./contact-dto";

/** FixedContainerViewModel */
export interface Building extends HateoasResource {
    buildingId: number;
    disabled: boolean;
    /** Parent building id */
    parentId?: number;
    /** So we can easily say where a shift is for example */
    parentName?: string;
    serviceRecipientId: number;
    serviceAllocationId: number;
    name: string | null;
    externalRef: string;
    resourceTypeId: number | undefined;
    resourceTypeName: string;
    calendarId: string;
    address?: Address;
    locationId: number;

    choicesMap?: StringToObjectMap<{id: number; name: string}>;
    textMap?: StringToStringMap;
    dateMap?: StringToStringMap;

    // client-side
    occupancyHistory?: OccupancyHistoryDto[];
    // client-side
}

export interface OccupancyHistoryDto extends AddressHistoryDto {
    building?: Building;
}
