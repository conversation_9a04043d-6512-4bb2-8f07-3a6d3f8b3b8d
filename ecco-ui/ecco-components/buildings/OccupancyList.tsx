import * as React from "react";
import {useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Divider,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid
} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancyHistory} from "../data/entityLoadHooks";
import {Building, fullAddress} from "ecco-dto";

interface OccupancyRecord {
    id: number;
    serviceRecipientId: number;
    validFrom: string;
    validTo?: string;
}

interface OccupancyHistorySectionProps {
    occupancyHistory?: Array<OccupancyRecord>;
}

/**
 * Helper function to format building display name with address and parent building
 */
const BuildingName: React.FC<{ building: Building }> = ({ building }) => {
    return (<>
        {building.name || `[b-id ${building.buildingId}]`}
        {building.parentName && <span style={{fontSize: 'small', fontWeight: 'bold'}}> {building.parentName}</span>}
        <Typography style={{fontSize: 'small'}}>{fullAddress(building.address)}</Typography>
        <Typography style={{fontSize: 'small'}}>{`[b-id ${building.buildingId}] [a-id ${building.locationId}]`}</Typography>
    </>);
}


/**
 * Component for rendering a single occupancy record
 */
const OccupancyItem: React.FC<{ occupancy: OccupancyRecord }> = ({ occupancy }) => (
    <ListItem>
        <ListItemText
            primary={`Service Recipient ID: ${occupancy.serviceRecipientId}`}
            secondary={
                <>
                    <Typography component="span" variant="body2">
                        From: {new Date(occupancy.validFrom).toLocaleDateString()}
                    </Typography>
                    {occupancy.validTo && (
                        <>
                            <br />
                            <Typography component="span" variant="body2">
                                To:{" "}
                                {new Date(occupancy.validTo).toLocaleDateString()}
                            </Typography>
                        </>
                    )}
                </>
            }
        />
    </ListItem>
);

/**
 * Component for displaying a section of occupancy records with a title
 */
const OccupancySection: React.FC<{ title: string; occupancies: Array<OccupancyRecord>; color?: string }> = ({ title, occupancies, color }) => {
    if (occupancies.length === 0) {
        return null;
    }

    return (
        <Box mb={2}>
            <Typography
                variant="subtitle2"
                component="h3"
                gutterBottom
                style={{color: color || "inherit", fontWeight: "bold"}}
            >
                {title}
            </Typography>
            <List dense>
                {occupancies.map((occupancy, index) => (
                    <React.Fragment key={occupancy.id}>
                        <OccupancyItem occupancy={occupancy} />
                        {index < occupancies.length - 1 && <Divider />}
                    </React.Fragment>
                ))}
            </List>
        </Box>
    );
};

/**
 * Component for displaying current occupancy for a building
 */
const OccupancyHistorySection: React.FC<OccupancyHistorySectionProps> = ({occupancyHistory}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return (
            <Typography variant="body2" color="textSecondary">
                -
            </Typography>
        );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Get current occupancies only
    const currentOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        return validFrom <= today && (!validTo || validTo >= today);
    });

    return (
        <OccupancySection
            title="Current Occupancy"
            occupancies={currentOccupancies}
            color="success.main"
        />
    );
};

/**
 * Component for displaying past and future occupancy in an accordion
 */
const OccupancyHistoryAccordion: React.FC<OccupancyHistorySectionProps> = ({occupancyHistory}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return null;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Categorize occupancy records
    const pastOccupancies = occupancyHistory.filter(occupancy => {
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        return validTo && validTo < today;
    });

    const futureOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        return validFrom > today;
    });

    // Don't show accordion if no past or future occupancies
    if (pastOccupancies.length === 0 && futureOccupancies.length === 0) {
        return null;
    }

    const totalCount = pastOccupancies.length + futureOccupancies.length;

    return (
        <Box mt={0}>
            <Accordion>
                <AccordionSummary>
                    <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                        Occupancy History ({totalCount} records)
                    </Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <Typography
                                variant="subtitle2"
                                style={{color: "info.main", fontWeight: "bold" /*, mb: 1*/}}
                            >
                                Future Occupancy ({futureOccupancies.length})
                            </Typography>
                            {futureOccupancies.length > 0 ? (
                                <List dense>
                                    {futureOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem occupancy={occupancy} />
                                            {index < futureOccupancies.length - 1 && <Divider />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            ) : (
                                <Typography variant="body2" color="textSecondary">
                                    No future occupancy
                                </Typography>
                            )}
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography
                                variant="subtitle2"
                                style={{color: "text.secondary", fontWeight: "bold" /*, mb: 1*/}}
                            >
                                Past Occupancy ({pastOccupancies.length})
                            </Typography>
                            {pastOccupancies.length > 0 ? (
                                <List dense>
                                    {pastOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem occupancy={occupancy} />
                                            {index < pastOccupancies.length - 1 && <Divider />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            ) : (
                                <Typography variant="body2" color="textSecondary">
                                    No past occupancy
                                </Typography>
                            )}
                        </Grid>
                    </Grid>
                </AccordionDetails>
            </Accordion>
        </Box>
    );
};

/**
 * Component for displaying occupancy lists
 */
export const OccupancyList: React.FC = () => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime());
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime());

    //useAppBarOptions(""); // not needed with proper AppBarBase defaultAppBar

    const {bldgsWithOccupancy, error, loading} = useBuildingsWithOccupancyHistory(
        from,
        to,
        pageNumber
    );

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box p={2}>
                <Typography color="error">error loading data: {error.message}</Typography>
            </Box>
        );
    }

    if (!bldgsWithOccupancy || bldgsWithOccupancy.length === 0) {
        return (
            <Box p={2}>
                <Typography>no units found</Typography>
            </Box>
        );
    }

    return (
        <Box p={2}>
            {bldgsWithOccupancy.map(building => (
                <Box key={building.buildingId} mb={2}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" component="h2" gutterBottom>
                                <BuildingName building={building}/>
                            </Typography>

                            <OccupancyHistorySection occupancyHistory={building.occupancyHistory} />
                        </CardContent>
                    </Card>
                    <OccupancyHistoryAccordion occupancyHistory={building.occupancyHistory} />
                </Box>
            ))}
        </Box>
    );
};
