import {Building, Client, IncidentDto, ReferralSummaryWithEntities, RepairDto, StaffJobDto, TaskNames, fullAddress} from "ecco-dto";
import {handleLazy, link, useIndividual, useServiceRecipientWithEntities, useServicesContext} from "ecco-components";
import {Card, CardContent, Grid, Typography} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, ReactNode} from "react";
import {staffDetail} from "../hr/StaffDetailForm";
import {clientDetail} from "../clientdetails/components/ClientDetailForm";
import {emergencyDetails} from "../referral/components/EmergencyDetailsForm";
import {StaffOverviewPane} from "../rota/components/StaffOverviewPane";
import {SrAvatarImageEditable} from "../contacts/components/ContactAvatar";
import {EccoDate} from "@eccosolutions/ecco-common";

const PadInCenter: FC = (props) => {
    return <Grid container justify="center">
        <Grid item xs={12} md={8}>
            {props.children}
        </Grid>
    </Grid>
}

const Item = (props: {label: string, content: string | number}) => {
    return !props.content
            ? null
            : <dd><Typography style={{fontSize: 'small'}}>{`${props.label} ${props.content}`}</Typography></dd>;
}
const ItemNode = (props: {label: string, content: ReactNode}) => {
    return !props.content
            ? null
            : <dd><div style={{fontSize: 'small'}}><div>{`${props.label}`}</div><div>{props.content}</div></div></dd>;
}

const InfoSectionStaff = (props: {workerJob: StaffJobDto}) => {
    const job = props.workerJob;
    return handleLazy(job
            ? <PadInCenter><StaffOverviewPane
                    srId={job.serviceRecipient.serviceRecipientId}
                    workerId={job.workerId}/></PadInCenter>
            : null);
}
const InfoSectionBuilding = (props: {building: Building}) => {
    const building = props.building;
    return <Grid item xs>
        <Grid container>
            {/* serviceName is shown in the ReferralFrame */}
            <dl>
                <Item label={'b-id:'} content={building.buildingId}/>
                <Item label={''} content={fullAddress(building.address)}/>
            </dl>
        </Grid>
    </Grid>;
}
// panel on the top left
const InfoSectionReferral = (props: { client: Client, referral: ReferralSummaryWithEntities }) => {
    const {sessionData} = useServicesContext();
    const {client, referral} = props;

    // NB can't use referral.supportWorkerDisplayName as the full referral isn't loaded (see findOneReferralSummaryByServiceRecipientIdUsingDto)
    const {contact: supportWorker} = useIndividual(referral?.supportWorkerId);

    const ops: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "short",
        day: "numeric",
        timeZone: "Europe/London"
    };
    const dteFmt = (dte: string) => EccoDate.parseIso8601(dte).toLocalJsDate().toLocaleString("en-GB", ops);
    const ageAndDobOrDied = client.age
            ? client.dateOfDeath
                    ? `${client.age}yrs (died ${dteFmt(client.dateOfDeath)}`
                    : `${client.age}yrs (${dteFmt(client.birthDate)})`
            : null;

    return <Grid item xs>
        <Grid container>
            {/* serviceName is shown in the ReferralFrame */}
            <dl>
                <Item label={'r-id:'} content={referral.referralCode || referral.referralId}/>
                <Item label={'c-id:'} content={referral.clientCode || referral.clientId}/>
                {client.externalSystemRef && <Item label={'ql-id:'} content={`${client.externalSystemSource}: ${client.externalSystemRef}`}/>}
                <Item label={''} content={ageAndDobOrDied}/>
                <Item label={''}
                      content={client.genderId && sessionData.getListDefinitionEntryById(client.genderId).getDisplayName()}/>
                {client.mobileNumber && <ItemNode label={''} content={<a href={`tel:${client.mobileNumber}`}>{client.mobileNumber}</a>}/>}
                {client.phoneNumber && <ItemNode label={''} content={<a href={`tel:${client.phoneNumber}`}>{client.phoneNumber}</a>}/>}
                {client.email && <ItemNode label={''} content={<a href={`mailto:${client.email}`}>{client.email}</a>}/>}
                {/*<Item label={''} content={client.preferredContactInfo}/>*/}
                <Item label={''} content={fullAddress(client.address)}/>
                <Item label={'NI'} content={client.ni}/>
                <Item label={'NHS'} content={client.nhs}/>
                {/* TODO check CLIENT_DETAIL_OPTIONAL_FIELDS for housing benefit */}
                <Item label={'HB'} content={client.housingBenefit}/>
                <Item label={supportWorker?.lastName && "worker" || ""} content={`${supportWorker?.firstName || ""} ${supportWorker?.lastName || ""}`}/>
            </dl>
        </Grid>
    </Grid>;
}

const InfoSectionIncident = (props: { incident: IncidentDto }) => {
    const {sessionData} = useServicesContext();
    const {incident} = props;

    const categoryDef = incident.categoryId && sessionData.getListDefinitionEntryById(incident.categoryId);

    return <Grid item xs>
        <Grid container>
            {/* serviceName is shown in the ReferralFrame */}
            <dl>
                {/*<Item label={'reported by: '} content={incident.reportedById}/>*/}
                <Item label={'reported by (name): '} content={incident.reportedBy}/>
                <Item label={'reported by (contact): '} content={incident.reportedByContact}/>
                <Item label={'category: '} content={categoryDef?.getDisplayName().concat(categoryDef?.isSignificant ? " (significant)" : "")}/>
                <Item label={'emergency services involved: '} content={incident.emergencyServicesInvolved == null ? "-" : incident.emergencyServicesInvolved ? "yes" : "no"}/>
                <Item label={'hospitalisation involved: '} content={incident.hospitalisationInvolved == null ? "-" : incident.hospitalisationInvolved ? "yes" : "no"}/>
            </dl>
        </Grid>
    </Grid>;
}

const InfoSectionRepair = (props: { repair: RepairDto }) => {
    const {sessionData} = useServicesContext();
    const {repair} = props;

    const categoryDef = repair.categoryId && sessionData.getListDefinitionEntryById(repair.categoryId);
    const priorityDef = repair.priorityId && sessionData.getListDefinitionEntryById(repair.priorityId);

    return <Grid item xs>
        <Grid container>
            {/* serviceName is shown in the ReferralFrame */}
            <dl>
                <Item label={'category: '} content={categoryDef?.getDisplayName().concat(categoryDef?.isSignificant ? " (significant)" : "")}/>
                <Item label={'priority: '} content={priorityDef?.getDisplayName()}/>
            </dl>
        </Grid>
    </Grid>;
}

// see context/referral.jsp (and id-badge.tag)
const InfoPanel: FC<{srId: number}> = ({srId}) => {
    const {context} = useServiceRecipientWithEntities(srId);

    if (!context) {
        return null;
    }

    // as per clientDetailAbstractRender defined optional fields
    const serviceType = context.serviceRecipient.features.getServiceTypeById(context.serviceType.id);
    const csvFields = serviceType && serviceType.getTaskDefinitionSetting(TaskNames.clientWithContact,  "clientDetailFields") || "";
    const showCode = csvFields.indexOf("code") > -1;

    let InfoPanel: JSX.Element = null;
    let infoEditable = true;
    switch (context.serviceRecipient.prefix) {
        case "r":
            InfoPanel = <InfoSectionReferral referral={context.referral} client={context.client}/>;
            break;
        case "w":
            InfoPanel = <InfoSectionStaff workerJob={context.workerJob}/>;
            break;
        case "b":
            InfoPanel = <InfoSectionBuilding building={context.building}/>
            infoEditable = false;
            break;
        case "i":
            InfoPanel = <InfoSectionIncident incident={context.incident}/>;
            infoEditable = false;
            break;
        case "m":
            InfoPanel = <InfoSectionRepair repair={context.repair}/>;
            infoEditable = false;
            break;
        default:
            InfoPanel = <div>unknown info panel</div>;
            infoEditable = false;
    }

    return <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
        <CardContent>
            <Grid container wrap={"nowrap"}>
                {/* TODO show with feature-toggle, maybe? <ecco:feature-query var="showAvatar" feature="client.showAvatar"/> */}
                {/* TODO edit/remove avatar */}
                <Grid item>
                    <SrAvatarImageEditable srId={srId} asIcon={false}/>
                </Grid>
                {InfoPanel}
                {infoEditable &&
                    <Grid item xs={1} container justify={"flex-end"}>
                        <Grid>
                            {link(<span className={"fa fa-edit"} style={{fontSize: "large"}}></span>,
                                    () => context.serviceRecipient.prefix == "w"
                                            ? staffDetail(context.serviceRecipient.serviceRecipientId, () => {
                                            })
                                            : clientDetail(srId, showCode, () => {})
                            )}
                            {link(<span className={"fa fa-life-ring"} style={{fontSize: "large", color: "orange"}}></span>,
                                    () => emergencyDetails(srId, () => {
                                    }))}
                        </Grid>
                    </Grid>
                }
            </Grid>
        </CardContent>
    </Card>
};

export default InfoPanel;