import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Lazy = require("lazy");
import types = require("./types");
    import SequenceAnalysis = types.SequenceAnalysis;
import {AnalysisContext} from "../charts/domain";
import {
    Building as BuildingDto,
    FinanceChargeDto,
    FinanceReceiptDto,
    OccupancyHistoryDto,
    ReferralSummaryDto,
    streetAddress
} from "ecco-dto";
import {
    columnMap, dateColumn, joinColumnMaps, joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../../controls/tableSupport";
import {EccoDate} from "@eccosolutions/ecco-common";
import {referralSummaryColumns} from "../tables/predefined-table-representations";
import {buildingOnlyColumns} from "./buildingAnalysis";


//*********************************
// Generalised functions


//*********************************
// Analysis: breakdown of finance

const financeChargeOnlyColumns = columnMap<FinanceChargeDto>(
    numberColumn("i-id", (row) => row.invoiceId),
    numberColumn("sr-id", (row) => row.serviceRecipientId),
    textColumn("description", (row) => row.description),
    numberColumn("b-id", (row) => row.buildingId),
    numberColumn("netAmount", (row) => row.netAmount),
    numberColumn("receiptTotal", (row) => row.receiptTotal),
    numberColumn("dueAmount", (row) => row.dueAmount)
);

const financeChargeToReferralColumns = joinNestedPathColumnMaps<FinanceChargeDto, ReferralSummaryDto | null>("r",
        row => row.referralSummary, referralSummaryColumns);

const financeChargeToBuildingColumns = joinNestedPathColumnMaps<FinanceChargeDto, BuildingDto | null>("b",
        row => row.building, buildingOnlyColumns);

const financeChargeWithReferralAndBuildingColumns = joinColumnMaps(financeChargeOnlyColumns, financeChargeToReferralColumns, financeChargeToBuildingColumns);

export class FinanceChargeAnalysis extends SequenceAnalysis<FinanceChargeDto> {
    constructor(ctx: AnalysisContext, data: Sequence<FinanceChargeDto>) {
        super(ctx, data, (item: FinanceChargeDto) => `${item.lineUuid}`);
        this.recordRepresentation = {
            "FinanceChargeOnly": financeChargeOnlyColumns,
            "FinanceChargeWithReferralAndBuilding": financeChargeWithReferralAndBuildingColumns,
        };
        this.derivativeAnalysers = {
        };
    }
}


const financeReceiptOnlyColumns = columnMap<FinanceReceiptDto>(
    numberColumn("rec-id", (row) => row.receiptId),
    numberColumn("sr-id", (row) => row.serviceRecipientId),
    dateColumn("received", (row) => EccoDate.parseIso8601(row.receivedDate)),
    textColumn("description", (row) => row.description),
    numberColumn("amount", (row) => row.amount)
);

const financeReceiptToReferralColumns = joinNestedPathColumnMaps<FinanceReceiptDto, ReferralSummaryDto | null>("r",
        row => row.referralSummary, referralSummaryColumns);

const financeReceiptWithReferralColumns = joinColumnMaps(financeReceiptOnlyColumns, financeReceiptToReferralColumns);

export class FinanceReceiptOnlyAnalysis extends SequenceAnalysis<FinanceReceiptDto> {
    constructor(ctx: AnalysisContext, data: Sequence<FinanceReceiptDto>) {
        super(ctx, data, (item: FinanceReceiptDto) => `${item.receiptId}`);
        this.recordRepresentation = {
            "FinanceReceiptOnly": financeReceiptOnlyColumns,
            "FinanceReceiptWithReferral": financeReceiptWithReferralColumns
        };
        this.derivativeAnalysers = {
        };
    }
}

export function financeCombined(charges: FinanceChargeDto[], receipts: FinanceReceiptDto[]): Sequence<FinanceChargeDto> {

    // collect the receipts not in any charge period
    const receiptsUsed: number[] = []

    charges.forEach(c => {
        const receiptsInPeriod = receipts.filter(r => {
            const chgFrom = EccoDate.parseIso8601FromDateTime(c.chargeFrom);
            const chgTo = c.chargeTo ? EccoDate.parseIso8601FromDateTime(c.chargeTo) : null;
            const received = EccoDate.parseIso8601(r.receivedDate);
            const gtFrom = chgFrom.compare(received) < 0;
            const loeTo = c.chargeTo ? (chgTo.compare(received) >= 0) : true;
            const receiptInPeriod = gtFrom && loeTo;
            if (receiptInPeriod) {
                receiptsUsed.push(r.receiptId);
            }
            return receiptInPeriod;
        });

        c.receiptTotal = receiptsInPeriod.reduce((prev, curr) => prev + curr.amount, 0);
        c.dueAmount = c.netAmount - c.receiptTotal;
    });

    receipts.filter(r => !receiptsUsed.includes(r.receiptId))
        .forEach(r => {
            const receiptOnlyCharge: FinanceChargeDto = {
                lineUuid: "", locked: false, rateCardId: 0, reverseCharge: false, taxRate: 0, type: "",
                invoiceId: 0,
                serviceRecipientId: r.serviceRecipientId,
                referralSummary: r.referralSummary,
                description: r.description,
                buildingId: 0,
                netAmount: null,
                receiptTotal: r.amount,
                dueAmount: -r.amount,
                links: []
            };
            charges.push(receiptOnlyCharge);
        });

    // NB we can have receipts not relevant to any period - but perhaps the report needs a discussion
    //const remainingReceipts = [];
    return Lazy(charges);
}


//*********************************
// Analysis: breakdown of occupancy

// see AddressHistory
const occupancyHistoryOnlyColumns = columnMap(
    numberColumn<OccupancyHistoryDto>("id", (row) => row.id),
    numberColumn<OccupancyHistoryDto>("o-id", (row) => row.addressId),
    numberColumn<OccupancyHistoryDto>("sr-id", (row) => row.serviceRecipientId),
    dateColumn<OccupancyHistoryDto>("valid from", (row) => EccoDate.parseIso8601FromDateTime(row.validFrom)),
    dateColumn<OccupancyHistoryDto>("valid to", (row) => EccoDate.parseIso8601FromDateTime(row.validTo!)),
    textColumn<OccupancyHistoryDto>("full address", (row) => fullAddress(row.address))
);

const occupancyHistoryToReferralSummaryColumns = joinNestedPathColumnMaps<OccupancyHistoryDto, ReferralSummaryDto>("r",
        (row) => row.referralSummary, referralSummaryColumns);
const occupancyHistoryToBuildingColumns = joinNestedPathColumnMaps<OccupancyHistoryDto, BuildingDto>("b",
        (row) => row.building, buildingOnlyColumns);

const occupancyHistoryWithColumns = joinColumnMaps(occupancyHistoryOnlyColumns,
                                                   occupancyHistoryToReferralSummaryColumns,
                                                   occupancyHistoryToBuildingColumns);

export class OccupancyHistoryAnalysis extends SequenceAnalysis<OccupancyHistoryDto> {
    constructor(ctx: AnalysisContext, data: Sequence<OccupancyHistoryDto>) {
        super(ctx, data, (item: OccupancyHistoryDto) => item.id.toString());
        this.recordRepresentation = {
            "OccupancyHistoryOnly": occupancyHistoryOnlyColumns,
            "OccupancyHistoryWith": occupancyHistoryWithColumns
        };
        this.derivativeAnalysers = {
        };
    }
}
